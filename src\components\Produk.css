/* Produk Component Styles - Responsive untuk semua ukuran layar */

/* Container utama produk */
.produk-container {
  font-family: 'Poppins', sans-serif;
  color: #0f2d67;
}

/* Header produk dengan gradient */
.produk-header {
  background: linear-gradient(135deg, var(--fallback-p, oklch(var(--p))), var(--fallback-s, oklch(var(--s))));
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Product card styling */
.product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  background: var(--fallback-b1, oklch(var(--b1)));
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: var(--fallback-p, oklch(var(--p) / 0.2));
}

.product-card.ring-2 {
  border-color: var(--fallback-p, oklch(var(--p)));
  box-shadow: 0 0 0 2px var(--fallback-p, oklch(var(--p) / 0.2));
}

/* Product image styling */
.product-card figure img {
  transition: transform 0.3s ease;
}

.product-card:hover figure img {
  transform: scale(1.05);
}

/* Price list container dengan animasi smooth */
.price-list-container {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.price-list-container.opacity-0 {
  opacity: 0;
  transform: translateY(16px);
  pointer-events: none;
}

.price-list-container.opacity-100 {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Table styling yang konsisten dengan theme */
.table {
  border-radius: 0.5rem;
  overflow: hidden;
}

.table th {
  background-color: var(--fallback-b2, oklch(var(--b2)));
  color: var(--fallback-p, oklch(var(--p)));
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem 0.5rem;
}

.table td {
  padding: 0.75rem 0.5rem;
  font-size: 0.875rem;
}

.table tbody tr:hover {
  background-color: var(--fallback-b2, oklch(var(--b2) / 0.5));
}

/* Badge styling untuk notes */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-info {
  background-color: var(--fallback-info, oklch(var(--in)));
  color: var(--fallback-info-content, oklch(var(--inc)));
}

.badge-success {
  background-color: var(--fallback-success, oklch(var(--su)));
  color: var(--fallback-success-content, oklch(var(--suc)));
}

/* Button styling yang konsisten dengan Navigation */
.btn {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  border-radius: 0.5rem;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary {
  background-color: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border-color: var(--fallback-p, oklch(var(--p)));
}

.btn-primary:hover {
  background-color: var(--fallback-p, oklch(var(--p) / 0.9));
  border-color: var(--fallback-p, oklch(var(--p) / 0.9));
}

.btn-primary.btn-active {
  background-color: var(--fallback-s, oklch(var(--s)));
  border-color: var(--fallback-s, oklch(var(--s)));
  color: var(--fallback-sc, oklch(var(--sc)));
}

/* Consistent layout for resolutions above 1000px (tablets and desktops) */
@media screen and (min-width: 1000px) {
  .produk-container {
    height: 100vh;
    max-height: 100vh;
  }

  .product-card {
    min-height: 400px;
  }

  .table th,
  .table td {
    padding: 0.875rem 0.75rem;
    font-size: 0.9rem;
  }

  /* Ensure consistent grid layout */
  .grid.lg\\:grid-cols-3 {
    grid-template-columns: 2fr 1fr;
  }
}

/* Responsive adjustments untuk tablet portrait */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .produk-header {
    padding: 2rem 1.5rem;
  }
  
  .product-card {
    min-height: 380px;
  }
  
  .price-list-container {
    margin-top: 1.5rem;
  }
}

/* Responsive adjustments untuk mobile */
@media screen and (max-width: 767px) {
  .produk-header {
    padding: 1.5rem 1rem;
  }
  
  .produk-header h1 {
    font-size: 1.875rem;
  }
  
  .produk-header p {
    font-size: 1rem;
  }
  
  .product-card {
    min-height: 350px;
  }
  
  .price-list-container {
    margin-top: 1rem;
  }
  
  .table th,
  .table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.8rem;
  }
  
  .badge {
    font-size: 0.7rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }
  
  .product-card:hover {
    transform: none;
  }
  
  .product-card:active {
    transform: scale(0.98);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .product-card {
    border: 2px solid var(--fallback-bc, oklch(var(--bc)));
  }
  
  .table th {
    border-bottom: 2px solid var(--fallback-bc, oklch(var(--bc)));
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .product-card,
  .price-list-container,
  .btn,
  .product-card figure img {
    transition: none;
  }
  
  .product-card:hover {
    transform: none;
  }
  
  .product-card:hover figure img {
    transform: none;
  }
}

/* Focus improvements untuk accessibility */
.product-card:focus-within {
  outline: 2px solid var(--fallback-p, oklch(var(--p)));
  outline-offset: 2px;
}

.btn:focus-visible {
  outline: 2px solid var(--fallback-p, oklch(var(--p)));
  outline-offset: 2px;
}

/* Loading state untuk gambar */
.product-card figure img {
  background-color: var(--fallback-b2, oklch(var(--b2)));
}

/* Sticky positioning untuk price list di desktop */
@media screen and (min-width: 1024px) {
  .price-list-container .card {
    position: sticky;
    top: 1rem;
  }
}

/* Grid layout optimizations */
@media screen and (min-width: 1200px) {
  .grid.xl\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1.5rem;
  }
}

/* Print styles */
@media print {
  .produk-header {
    background: none !important;
    color: black !important;
  }
  
  .product-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .btn {
    display: none;
  }
}
