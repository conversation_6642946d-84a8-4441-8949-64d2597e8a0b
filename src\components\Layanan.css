/* Layanan Component Styles - Responsive untuk semua ukuran layar */

/* Main container - No scroll layout */
.layanan-container {
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg,
    oklch(var(--p) / 0.15) 0%,
    oklch(var(--s) / 0.1) 50%,
    oklch(var(--a) / 0.05) 100%);
  color: oklch(var(--bc));
  font-family: 'Poppins', sans-serif;
  /* Android/Web font consistency */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
  display: flex;
  flex-direction: column;
}

/* Main content area - Flexible height for no-scroll layout */
.layanan-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Title section */
.layanan-title-section {
  text-align: left;
  margin-bottom: 1rem;
  padding-left: 1rem;
  flex-shrink: 0;
}

.layanan-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: oklch(var(--p));
  letter-spacing: 0.05em;
  margin: 0;
}

/* Services grid - Flexible height for no-scroll */
.layanan-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  flex: 1;
  overflow: hidden;
  align-content: start;
}

/* Service card */
.layanan-card {
  background: oklch(var(--b1));
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 25px -5px oklch(var(--p) / 0.1),
              0 8px 10px -6px oklch(var(--p) / 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid oklch(var(--b3));
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.layanan-card:nth-child(1) { animation-delay: 0.1s; }
.layanan-card:nth-child(2) { animation-delay: 0.2s; }
.layanan-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.layanan-card:hover,
.layanan-card:focus {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px -10px oklch(var(--p) / 0.15),
              0 16px 20px -12px oklch(var(--p) / 0.15);
  outline: 2px solid oklch(var(--p));
  outline-offset: 2px;
}

.layanan-card:focus {
  outline: 2px solid oklch(var(--p));
  outline-offset: 2px;
}

/* Service image - Reduced height for no-scroll layout */
.layanan-image-container {
  width: 100%;
  height: 120px;
  overflow: hidden;
  position: relative;
}

.layanan-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.layanan-card:hover .layanan-image {
  transform: scale(1.05);
}

/* Service content - Reduced padding for no-scroll layout */
.layanan-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Service header section */
.layanan-header-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.layanan-icon {
  font-size: 1.5rem;
  background: oklch(var(--p) / 0.1);
  padding: 0.5rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 3rem;
  min-height: 3rem;
}

.layanan-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: oklch(var(--p));
  margin: 0;
  line-height: 1.3;
}

/* Service description */
.layanan-description {
  font-size: 0.85rem;
  color: oklch(var(--bc) / 0.8);
  margin-bottom: 0.75rem;
  line-height: 1.4;
  font-weight: 500;
}

/* Service features - Compact layout */
.layanan-features {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow: hidden;
}

.layanan-feature-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  line-height: 1.3;
}

.layanan-feature-item:last-child {
  margin-bottom: 0;
}

.layanan-feature-icon {
  color: oklch(var(--s));
  font-weight: 600;
  font-size: 0.8rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.layanan-feature-text {
  color: oklch(var(--bc) / 0.7);
  flex: 1;
}



/* Responsive Design */

/* Consistent layout for resolutions above 1000px (tablets and desktops) */
@media screen and (min-width: 1000px) {
  .layanan-container {
    height: 100vh;
    max-height: 100vh;
  }

  .layanan-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .layanan-title {
    font-size: 2rem;
  }

  .layanan-image-container {
    height: 140px;
  }

  .layanan-content {
    padding: 1.25rem;
  }

  .layanan-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .layanan-feature-item {
    font-size: 0.85rem;
    margin-bottom: 0.6rem;
  }
}

/* Tablet Portrait (below 1000px) */
@media screen and (max-width: 999px) {
  .layanan-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
  }

  .layanan-title {
    font-size: 1.5rem;
  }
}

/* Mobile */
@media screen and (max-width: 767px) {
  .layanan-main {
    padding: 0 0.75rem;
  }

  .layanan-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .layanan-title {
    font-size: 1.25rem;
  }

  .layanan-card {
    border-radius: 0.75rem;
  }

  .layanan-content {
    padding: 0.75rem;
  }

  .layanan-card-title {
    font-size: 1rem;
  }

  .layanan-description {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }

  .layanan-feature-item {
    font-size: 0.75rem;
    margin-bottom: 0.4rem;
  }

  .layanan-image-container {
    height: 100px;
  }
}
