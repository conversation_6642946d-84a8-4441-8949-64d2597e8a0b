import React from 'react';

/**
 * Props for UniversalHeader component
 */
interface UniversalHeaderProps {
  className?: string;
}

/**
 * Universal Header Component
 * Consistent header with company logo and name used across all pages
 * Maintains identical styling for resolutions above 1000px
 */
const UniversalHeader: React.FC<UniversalHeaderProps> = ({ className = '' }) => {
  return (
    <header className={`w-full max-w-7xl mx-auto px-4 py-3 flex items-center justify-start ${className}`}>
      <div className="flex items-center gap-4">
        <img
          src="/images/icons/icon-192x192.png"
          alt="PT Putera Wibowo Borneo Logo"
          className="w-10 h-10 object-contain rounded-lg shadow-lg shadow-primary/20"
        />
        <div>
          <h1 className="text-xl font-semibold text-primary tracking-wide">
            PT PUTERA WIBOWO BORNEO
          </h1>
          <p className="text-sm text-base-content/70 italic">
            "Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"
          </p>
        </div>
      </div>
    </header>
  );
};

export default UniversalHeader;
